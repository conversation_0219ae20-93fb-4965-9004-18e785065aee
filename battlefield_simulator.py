#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
战场单位模拟器
每分钟发送一次敌方和我方单位的位置和状态变化数据
"""
import asyncio
import json
import time
import random
import math
import logging
import aiohttp
from datetime import datetime
from typing import Dict, List, Any
from dataclasses import dataclass, asdict
from fastapi import BackgroundTasks
import websockets

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class Position:
    """位置信息"""
    longitude: float  # 经度
    latitude: float   # 纬度
    altitude: float   # 高度

@dataclass
class UnitStatus:
    """单位状态"""
    health: float      # 生命值 (0-100)
    ammo: float       # 弹药状态 (0-100)
    fuel: float       # 燃料状态 (0-100)
    operational: bool  # 是否可操作

@dataclass
class BattlefieldUnit:
    """战场单位"""
    id: str
    name: str          # 武器名称
    type: str          # 武器类型
    side: str          # 属方 (敌方/我方)
    position: Position
    status: UnitStatus
    threat_level: str  # 威胁等级
    confidence: float  # 置信度
    last_seen: str     # 最后发现时间
    speed: float       # 移动速度 (km/h)
    heading: float     # 航向角度 (0-360度)

class BattlefieldSimulator:
    """战场模拟器"""

    def __init__(self, api_url: str = "http://172.17.110.105:8010"):
        self.api_url = api_url
        self.enemy_units = []
        self.friendly_units = []
        self.simulation_time = 0
        self.running = False
        self.session = None
        self.websocket = None  # aiohttp session

        # 初始化单位
        self._initialize_units()
    
    def _initialize_units(self):
        """初始化战场单位"""
        # 创建3个敌方单位
        self.enemy_units = [
            BattlefieldUnit(
                id="ENEMY_001",
                name="T-90主战坦克",
                type="主战坦克",
                side="敌方",
                position=Position(116.3974, 39.9042, 45.2),
                status=UnitStatus(100.0, 85.0, 90.0, True),
                threat_level="高",
                confidence=0.95,
                last_seen=datetime.now().isoformat(),
                speed=25.0,  # km/h
                heading=45.0
            ),
            BattlefieldUnit(
                id="ENEMY_002",
                name="BTR-80装甲运兵车",
                type="装甲车",
                side="敌方",
                position=Position(116.4174, 39.9242, 52.1),
                status=UnitStatus(100.0, 70.0, 80.0, True),
                threat_level="中",
                confidence=0.88,
                last_seen=datetime.now().isoformat(),
                speed=35.0,
                heading=120.0
            ),
            BattlefieldUnit(
                id="ENEMY_003",
                name="苏-35战斗机",
                type="战斗机",
                side="敌方",
                position=Position(116.3574, 39.8842, 8500.0),
                status=UnitStatus(100.0, 90.0, 75.0, True),
                threat_level="极高",
                confidence=0.92,
                last_seen=datetime.now().isoformat(),
                speed=800.0,
                heading=270.0
            )
        ]
        
        # 创建2个我方单位
        self.friendly_units = [
            BattlefieldUnit(
                id="FRIENDLY_001",
                name="99A主战坦克",
                type="主战坦克",
                side="我方",
                position=Position(116.3774, 39.8942, 48.5),
                status=UnitStatus(100.0, 95.0, 90.0, True),
                threat_level="",
                confidence=1.0,
                last_seen=datetime.now().isoformat(),
                speed=30.0,
                heading=315.0
            ),
            BattlefieldUnit(
                id="FRIENDLY_002",
                name="歼-20战斗机",
                type="战斗机",
                side="我方",
                position=Position(116.3674, 39.8742, 9200.0),
                status=UnitStatus(100.0, 100.0, 85.0, True),
                threat_level="",
                confidence=1.0,
                last_seen=datetime.now().isoformat(),
                speed=900.0,
                heading=180.0
            )
        ]
        
        logger.info(f"初始化完成: {len(self.enemy_units)}个敌方单位, {len(self.friendly_units)}个我方单位")
    
    def _update_unit_position(self, unit: BattlefieldUnit, time_delta_minutes: float):
        """更新单位位置"""
        # 计算移动距离 (km)
        distance_km = (unit.speed * time_delta_minutes) / 60.0
        
        # 转换为经纬度变化 (粗略计算)
        # 1度经度 ≈ 111km (在北纬39度附近约为85km)
        # 1度纬度 ≈ 111km
        lat_change = (distance_km * math.cos(math.radians(unit.heading))) / 111.0
        lon_change = (distance_km * math.sin(math.radians(unit.heading))) / 85.0
        
        unit.position.latitude += lat_change
        unit.position.longitude += lon_change
        
        # 添加随机扰动
        unit.position.latitude += random.uniform(-0.0001, 0.0001)
        unit.position.longitude += random.uniform(-0.0001, 0.0001)
        
        # 飞行器高度变化
        if unit.type == "战斗机":
            unit.position.altitude += random.uniform(-100, 100)
            unit.position.altitude = max(1000, min(15000, unit.position.altitude))
    
    def _update_unit_status(self, unit: BattlefieldUnit):
        """更新单位状态"""
        # 模拟燃料消耗
        fuel_consumption = random.uniform(0.5, 2.0)
        unit.status.fuel = max(0, unit.status.fuel - fuel_consumption)
        
        # 模拟弹药消耗 (战斗时)
        if random.random() < 0.1:  # 10%概率发生战斗
            ammo_consumption = random.uniform(2.0, 8.0)
            unit.status.ammo = max(0, unit.status.ammo - ammo_consumption)
        
        # 模拟损伤
        if random.random() < 0.05:  # 5%概率受到损伤
            damage = random.uniform(1.0, 10.0)
            unit.status.health = max(0, unit.status.health - damage)
        
        # 更新操作状态
        unit.status.operational = (
            unit.status.health > 20 and 
            unit.status.fuel > 5 and 
            unit.status.ammo > 0
        )
        
        # 随机改变航向
        if random.random() < 0.3:  # 30%概率改变航向
            unit.heading += random.uniform(-30, 30)
            unit.heading = unit.heading % 360
        
        # 更新最后发现时间
        unit.last_seen = datetime.now().isoformat()
        
        # 更新置信度
        if unit.side == "敌方":
            unit.confidence = max(0.5, unit.confidence + random.uniform(-0.05, 0.02))
    
    def _create_battlefield_data(self) -> Dict[str, Any]:
        """创建战场数据包"""
        return {
            "timestamp": datetime.now().isoformat(),
            "simulation_time": self.simulation_time,
            "enemy_units": [asdict(unit) for unit in self.enemy_units],
            "friendly_units": [asdict(unit) for unit in self.friendly_units],
            "battlefield_status": {
                "total_enemy": len([u for u in self.enemy_units if u.status.operational]),
                "total_friendly": len([u for u in self.friendly_units if u.status.operational]),
                "active_threats": len([u for u in self.enemy_units if u.status.operational and u.threat_level in ["高", "极高"]])
            }
        }
    
    async def _send_data_to_api(self, data: Dict[str, Any], api: str):
        """发送数据到API（非阻塞）"""
        if not self.session:
            self.session = aiohttp.ClientSession()

        try:
            start_time = asyncio.get_event_loop().time()
            async with self.session.post(
                api,
                json=data,
                timeout=aiohttp.ClientTimeout(total=60)
            ) as response:
                elapsed_time = asyncio.get_event_loop().time() - start_time

                if response.status == 200:
                    logger.info(f"数据发送成功 - 响应时间: {elapsed_time:.2f}s")
                    result = await response.json()
                    logger.info(f"分析结果状态: {result.get('status', 'unknown')}")
                else:
                    logger.error(f"API调用失败 - 状态码: {response.status}")

        except aiohttp.ClientError as e:
            logger.error(f"发送数据失败: {e}")
        except asyncio.TimeoutError:
            logger.error("发送数据超时")
        except Exception as e:
            logger.error(f"发送数据异常: {e}")
    
    async def _send_data_to_websocket(self, data: Dict[str, Any], api: str):
        """发送数据到API（非阻塞）"""
        if not self.websocket:
            self.websocket = await websockets.connect(api)
        
        ping_message = {
            "type": "ping",
            "data": data,
            "timestamp": datetime.now().isoformat()
        }
        
        try:
            await self.websocket.send(json.dumps(ping_message))
            logger.info("发送心跳包")
        except Exception as e:
            logger.error(f"发送心跳包失败: {e}")
    
    def update_simulation(self, time_delta_minutes: float = 3.0):
        """更新模拟状态"""
        self.simulation_time += time_delta_minutes
        
        logger.info(f"=== 模拟时间: {self.simulation_time:.1f}秒 ===")
        
        # 更新所有单位
        all_units = self.enemy_units + self.friendly_units
        # print("所有单位：", all_units)
        for unit in all_units:
            if unit.status.operational:
                self._update_unit_position(unit, time_delta_minutes)
                self._update_unit_status(unit)
                
                logger.info(f"{unit.side} {unit.name}: "
                          f"位置({unit.position.longitude:.4f}, {unit.position.latitude:.4f}), "
                          f"状态(血量:{unit.status.health:.1f}%, 燃料:{unit.status.fuel:.1f}%, "
                          f"弹药:{unit.status.ammo:.1f}%)")
    
    def append_to_json_file(self, filename, new_data):
        """向JSON文件追加数据"""
        try:
            # 读取现有数据
            with open(filename, 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
        except FileNotFoundError:
            # 如果文件不存在，创建新列表
            existing_data = []
        
        # 添加新数据
        existing_data.append(new_data)
        
        # 保存回文件
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(existing_data, f, ensure_ascii=False, indent=2)
    
    async def start_simulation(self, generate_interval: int = 3, send_interval: int = 60):
        """开始模拟"""
        self.running = True
        logger.info(f"开始战场模拟 - 数据生成间隔: {generate_interval}秒, 发送间隔: {send_interval}秒")
        
        counter = 0
        # accumulated_data = []  # 存储累积的数据
        
        while self.running:
            try:
                # 更新模拟状态
                self.update_simulation()
                
                # 每3秒生成数据
                battlefield_data = self._create_battlefield_data()
                # self.append_to_json_file("raw_data.json", battlefield_data)
                print(battlefield_data)
                # accumulated_data.append(battlefield_data)
                counter += 1
                logger.debug(f"生成第 {counter} 次数据")

                # 每60秒发送一次数据（60/3 = 20次数据）
                if counter >= (send_interval // generate_interval):
                    # logger.info(f"累积了 {len(accumulated_data)} 条数据，准备发送...")
                    asyncio.create_task(self._send_data_to_api(battlefield_data, f"{self.api_url}/battlefield/analyze"))
                    # asyncio.create_task(self._send_data_to_websocket(battlefield_data, "ws://localhost:8010/battlefield/latest"))
                    # accumulated_data = []  # 清空累积的数据
                    counter = 0  # 重置计数器
                
                # 等待下次生成
                await asyncio.sleep(generate_interval)
                
            except KeyboardInterrupt:
                logger.info("收到停止信号，正在停止模拟...")
                break
            except Exception as e:
                logger.error(f"模拟过程中发生错误: {e}")
                # 发送累积的数据（如果有）
                counter = 0
                await asyncio.sleep(5)  # 错误后等待5秒再继续
    
    async def stop_simulation(self):
        """停止模拟"""
        self.running = False
        if self.session:
            await self.session.close()
            self.session = None
        logger.info("模拟已停止")

async def main():
    """主程序"""
    # 创建模拟器
    simulator = BattlefieldSimulator()

    # 开始模拟 (每60秒发送一次数据)
    try:
        await simulator.start_simulation()
    except KeyboardInterrupt:
        await simulator.stop_simulation()
        logger.info("程序已退出")

if __name__ == "__main__":
    asyncio.run(main())
