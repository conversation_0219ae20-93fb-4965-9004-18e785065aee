#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版FastAPI WebSocket服务器用于调试
"""
import json
import logging
import sys
import os
from datetime import datetime
from typing import Set
from contextlib import asynccontextmanager
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# 简单的日志设置
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# WebSocket连接管理器
class ConnectionManager:
    """WebSocket连接管理器"""

    def __init__(self):
        self.active_connections: Set[WebSocket] = set()

    async def connect(self, websocket: WebSocket):
        """接受新的WebSocket连接"""
        print("ConnectionManager.connect() 被调用")
        try:
            await websocket.accept()
            print("WebSocket.accept() 成功")
            self.active_connections.add(websocket)
            print(f"连接已添加到活跃连接列表，当前连接数: {len(self.active_connections)}")
            logger.info(f"新的WebSocket连接已建立，当前连接数: {len(self.active_connections)}")
        except Exception as e:
            print(f"WebSocket.accept() 失败: {e}")
            raise

    def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        self.active_connections.discard(websocket)
        logger.info(f"WebSocket连接已断开，当前连接数: {len(self.active_connections)}")

# 创建管理器实例
manager = ConnectionManager()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    logger.info("调试FastAPI WebSocket服务启动")
    yield
    logger.info("调试FastAPI WebSocket服务关闭")

# 创建FastAPI应用
app = FastAPI(
    title="调试WebSocket API",
    description="用于调试WebSocket问题的简化版本",
    version="1.0.0",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "调试FastAPI WebSocket服务器"}

@app.websocket("/battlefield/latest")
async def websocket_latest_analysis(websocket: WebSocket):
    """WebSocket端点 - 实时推送最新分析结果"""
    print("=== WebSocket处理函数被调用 ===")
    await manager.connect(websocket)
    print("连接成功")

    try:
        print("开始WebSocket消息循环")
        while True:
            try:
                print("等待接收客户端消息...")
                # 接收客户端消息（心跳包等）
                message = await websocket.receive_text()
                print(f"收到原始消息: {message}")

                # 处理客户端请求
                client_message = json.loads(message)
                print("接收到消息")
                print(client_message)

                if client_message.get("type") == "ping":
                    # 响应心跳包
                    pong_message = {
                        "type": "pong",
                        "timestamp": datetime.now().isoformat()
                    }
                    print(f"发送pong响应: {pong_message}")
                    await websocket.send_text(json.dumps(pong_message, ensure_ascii=False))
                    print("pong响应已发送")

            except json.JSONDecodeError as e:
                print(f"JSON解析错误: {e}")
                error_message = {
                    "type": "error",
                    "message": "Invalid JSON format",
                    "timestamp": datetime.now().isoformat()
                }
                await websocket.send_text(json.dumps(error_message, ensure_ascii=False))
            except Exception as e:
                print(f"处理消息时出错: {e}")
                # 继续循环，不断开连接

    except WebSocketDisconnect:
        print("WebSocket连接断开")
        manager.disconnect(websocket)
    except Exception as e:
        print(f"WebSocket处理出错: {e}")
        manager.disconnect(websocket)

if __name__ == "__main__":
    print("启动调试FastAPI WebSocket服务器...")
    uvicorn.run(app, host="0.0.0.0", port=8010)
