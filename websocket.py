import asyncio
from datetime import datetime
import websockets
import json
import time

class WebSocketServer:
    def __init__(self):
        self.clients = set()
        self.heartbeat_timeout = 600  # 心跳超时时间
        
    async def register_client(self, websocket):
        """注册客户端"""
        self.clients.add(websocket)
        print(f"客户端已连接: {websocket.remote_address}")
        
    async def unregister_client(self, websocket):
        """注销客户端"""
        self.clients.discard(websocket)
        print(f"客户端已断开: {websocket.remote_address}")
        
    async def handle_client(self, websocket):
        """处理客户端连接（修正版本）"""
        await self.register_client(websocket)
        last_heartbeat = time.time()
        
        try:
            # 发送欢迎消息
            welcome_msg = {
                "type": "welcome",
                "message": "连接成功"
            }
            await websocket.send(json.dumps(welcome_msg))
            
            async for message in websocket:
                try:
                    data = json.loads(message)
                    print(f"收到消息: {data}")
                    
                    # 处理心跳
                    if data.get("type") == "heartbeat":
                        last_heartbeat = time.time()
                        # 发送心跳响应
                        response = {
                            "type": "heartbeat_response",
                            "timestamp": data.get("timestamp"),
                            "server_time": int(time.time())
                        }
                        await websocket.send(json.dumps(response))
                        print("发送心跳响应")
                        
                    elif data.get("type") == "ping":
                        # 处理ping消息
                        response = {
                            "type": "pong",
                            "timestamp": data.get("timestamp"),
                            "server_time": int(time.time())
                        }
                        await websocket.send(json.dumps(response))
                        print("发送pong响应")
                        
                    else:
                        # 处理其他消息
                        await self.broadcast_message(data, websocket)
                        
                except json.JSONDecodeError:
                    print("无效的JSON消息")
                    
                # 检查心跳超时
                if time.time() - last_heartbeat > self.heartbeat_timeout:
                    print("客户端心跳超时")
                    break
                    
        except websockets.exceptions.ConnectionClosed:
            print("客户端连接已关闭")
        except Exception as e:
            print(f"处理客户端消息时出错: {e}")
        finally:
            await self.unregister_client(websocket)
            
    async def broadcast_message(self, result, sender):
        """广播消息给所有客户端（除了发送者）"""
        print("广播")
        if self.clients:
            # message["timestamp"] = int(time.time())
            message = {
                "type": "analysis_update",
                "data": result.model_dump(),
                "timestamp": datetime.now().isoformat()
            }
            for client in self.clients.copy():
                if client != sender:
                    try:
                        await client.send(json.dumps(message))
                    except:
                        await self.unregister_client(client)
                        
    async def start_server(self, host="localhost", port=8765):
        """启动服务器"""
        # 注意：这里只传一个参数给handle_client
        server = await websockets.serve(self.handle_client, host, port)
        print(f"WebSocket服务器启动在 ws://{host}:{port}")
        await server.wait_closed()

server = WebSocketServer()
# 使用示例
if __name__ == "__main__":
    asyncio.run(server.start_server())