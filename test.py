# # python_websocket_client.py
# import asyncio
# import websockets
# import json
# from datetime import datetime

# async def battlefield_client():
#     uri = "ws://localhost:8010/battlefield/latest"
    
#     try:
#         async with websockets.connect(uri) as websocket:
#             print("WebSocket 连接成功")
            
#             # 持续接收消息
#             while True:
#                 try:
#                     # 发送ping消息
#                     ping_message = {
#                         "type": "ping",
#                         "data": {
#                             "battle_id": "test_001",
#                             "action": "request_analysis",
#                             "timestamp": datetime.now().isoformat()
#                         }
#                     }
                    
#                     await websocket.send(json.dumps(ping_message))
#                     print("已发送ping消息")

#                     message = await websocket.recv()
#                     data = json.loads(message)
#                     print(f"收到消息: {data}")
                    
#                     if data.get("type") == "DATA_UPDATED":
#                         print("数据更新:", data.get("data"))
                        
#                 except websockets.exceptions.ConnectionClosed:
#                     print("连接已关闭")
#                     break
                    
#     except Exception as e:
#         print(f"连接错误: {e}")

# # 运行客户端
# if __name__ == "__main__":
#     asyncio.run(battlefield_client())

import asyncio
import websockets
import json
from datetime import datetime

async def maintain_connection():
    uri = "ws://localhost:8765"
    # uri = "ws://*************:8000/ws/battlefield"
    
    while True:
        try:
            async with websockets.connect(uri) as websocket:
                print("连接成功")

                # 立即发送一个心跳包测试
                initial_heartbeat = {
                    "type": "ping",
                    "timestamp": datetime.now().isoformat()
                }
                await websocket.send(json.dumps(initial_heartbeat))
                print("已发送初始心跳包")

                # 创建心跳任务
                async def send_heartbeat():
                    while True:
                        await asyncio.sleep(30)  # 每30秒发送一次心跳
                        heartbeat = {
                            "type": "ping",
                            "timestamp": datetime.now().isoformat()
                        }
                        await websocket.send(json.dumps(heartbeat))
                        print("已发送心跳包")

                # 创建接收消息任务
                heartbeat_task = asyncio.create_task(send_heartbeat())
                
                try:
                    while True:
                        # 创建接收消息任务
                        # heartbeat_task = asyncio.create_task(send_heartbeat())
                        message = await websocket.recv()
                        data = json.loads(message)
                        print(f"收到消息: {data}")
                except websockets.exceptions.ConnectionClosed:
                    print("连接断开，尝试重连...")
                    heartbeat_task.cancel()
                    await asyncio.sleep(5)  # 等待5秒后重连
                    
        except Exception as e:
            print(f"连接错误: {e}")
            await asyncio.sleep(5)

# 运行客户端
asyncio.run(maintain_connection())