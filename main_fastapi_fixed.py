#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
战场态势分析FastAPI服务
接收实时战场数据，进行分析并返回结果给前端
"""
import json
import time
import logging
import sys
import os
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional, Set
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException, BackgroundTasks, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn
import queue
import threading
from websocket import server

# 添加src路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    setup_logging()
    logger.info("战场态势分析API服务启动")
    yield
    # 关闭时清理
    logger.info("战场态势分析API服务关闭")

# 创建FastAPI应用
app = FastAPI(
    title="战场态势分析API",
    description="实时战场数据分析和威胁评估系统",
    version="1.0.0",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# WebSocket连接管理器
class ConnectionManager:
    """WebSocket连接管理器"""

    def __init__(self):
        self.active_connections: Set[WebSocket] = set()

    async def connect(self, websocket: WebSocket):
        """接受新的WebSocket连接"""
        print("ConnectionManager.connect() 被调用")
        try:
            await websocket.accept()
            print("WebSocket.accept() 成功")
            self.active_connections.add(websocket)
            print(f"连接已添加到活跃连接列表，当前连接数: {len(self.active_connections)}")
            logger.info(f"新的WebSocket连接已建立，当前连接数: {len(self.active_connections)}")
        except Exception as e:
            print(f"WebSocket.accept() 失败: {e}")
            raise

    def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        self.active_connections.discard(websocket)
        logger.info(f"WebSocket连接已断开，当前连接数: {len(self.active_connections)}")

    async def send_personal_message(self, message: str, websocket: WebSocket):
        """发送个人消息"""
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"发送个人消息失败: {e}")
            self.disconnect(websocket)

    async def broadcast(self, message: str):
        """广播消息给所有连接的客户端"""
        if not self.active_connections:
            logger.debug("没有活跃的WebSocket连接，跳过广播")
            return

        logger.info(f"向 {len(self.active_connections)} 个客户端广播消息")
        disconnected_connections = set()

        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"广播消息失败: {e}")
                disconnected_connections.add(connection)

        # 清理断开的连接
        for connection in disconnected_connections:
            self.disconnect(connection)

# 全局变量
logger = None
latest_analysis_result = None
analysis_history = []
manager = ConnectionManager()

def setup_logging():
    """设置详细的日志记录"""
    global logger
    
    # 创建logs目录
    if not os.path.exists("logs"):
        os.makedirs("logs")
    
    # 生成带时间戳的日志文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"logs/battlefield_api_{timestamp}.log"
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"日志系统初始化完成 - 日志文件: {log_filename}")
    return log_filename

# 立即初始化日志系统（确保在任何地方都能使用）
def init_logger_immediately():
    """立即初始化日志系统"""
    global logger
    if logger is None:
        setup_logging()

# 在模块加载时就初始化日志
init_logger_immediately()



# Pydantic模型定义
class Position(BaseModel):
    longitude: float
    latitude: float
    altitude: float

class UnitStatus(BaseModel):
    health: float
    ammo: float
    fuel: float
    operational: bool

class BattlefieldUnit(BaseModel):
    id: str
    name: str
    type: str
    side: str
    position: Position
    status: UnitStatus
    threat_level: Optional[str] = ""
    confidence: float
    last_seen: str
    speed: Optional[float] = 0.0
    heading: Optional[float] = 0.0

class BattlefieldData(BaseModel):
    timestamp: str
    simulation_time: float
    enemy_units: List[BattlefieldUnit]
    friendly_units: List[BattlefieldUnit]
    battlefield_status: Dict[str, Any]

class AnalysisResult(BaseModel):
    status: str
    analysis_id: str
    timestamp: str
    processing_time_ms: float
    battlefield_summary: Dict[str, Any]
    threat_assessment: Optional[Dict[str, Any]] = None
    tactical_recommendation: Optional[Dict[str, Any]] = None
    llm_analysis: Optional[Dict[str, Any]] = None
    weapon_knowledge: Optional[Dict[str, Any]] = None

def query_weapon_knowledge_safe(weapon_name: str, weapon_type: str) -> Dict[str, Any]:
    """安全的武器知识库查询"""
    start_time = time.time()
    logger.info(f"开始查询武器知识库: {weapon_name} ({weapon_type})")
    
    try:
        from src.battlefield_data_service import BattlefieldDataService
        
        # 尝试初始化数据服务
        data_service = BattlefieldDataService()
        
        # 查询武器知识
        result = data_service.query_weapon_knowledge(weapon_name, weapon_type)
        
        processing_time = round((time.time() - start_time) * 1000, 2)
        
        logger.info(f"武器知识库查询完成 - 耗时: {processing_time}ms")
        
        # 安全的JSON序列化测试
        try:
            json.dumps(result, ensure_ascii=False)
            # 正确判断查询状态
            if result.get('status') == 'not_found':
                logger.info(f"查询结果状态: not_found")
            elif 'weapon_name' in result:
                logger.info(f"查询结果状态: found")
            else:
                logger.info(f"查询结果状态: unknown")
        except Exception as json_error:
            logger.warning(f"查询结果JSON序列化测试失败: {json_error}")
        
        return {
            "result": result,
            "processing_time_ms": processing_time
        }
        
    except Exception as e:
        processing_time = round((time.time() - start_time) * 1000, 2)
        logger.error(f"武器知识库查询失败: {e}")
        
        # 使用模拟数据作为备用方案
        logger.info(f"使用模拟数据作为备用方案: {weapon_name}")
        mock_result = create_mock_weapon_knowledge(weapon_name, weapon_type)
        
        return {
            "result": mock_result,
            "processing_time_ms": processing_time,
            "note": "使用模拟数据"
        }

def create_mock_weapon_knowledge(weapon_name: str, weapon_type: str) -> Dict[str, Any]:
    """创建模拟武器知识数据"""
    knowledge_db = {
        "T-90主战坦克": {
            "status": "found",
            "weapon_data": {
                "weapon_name": "T-90主战坦克",
                "weapon_type": "主战坦克",
                "firepower_rating": 9,
                "protection_rating": 8,
                "mobility_rating": 7,
                "main_gun": "125mm滑膛炮",
                "armor_type": "复合装甲+反应装甲",
                "max_speed": "60km/h",
                "crew": 3,
                "weight": "46.5吨",
                "combat_range": "500km"
            }
        },
        "BTR-80装甲运兵车": {
            "status": "found",
            "weapon_data": {
                "weapon_name": "BTR-80装甲运兵车",
                "weapon_type": "装甲车",
                "firepower_rating": 5,
                "protection_rating": 6,
                "mobility_rating": 8,
                "main_gun": "14.5mm机枪",
                "armor_type": "钢装甲",
                "max_speed": "80km/h",
                "crew": 3,
                "capacity": "7名步兵",
                "amphibious": "是"
            }
        },
        "苏-35战斗机": {
            "status": "found",
            "weapon_data": {
                "weapon_name": "苏-35战斗机",
                "weapon_type": "战斗机",
                "firepower_rating": 9,
                "protection_rating": 7,
                "mobility_rating": 10,
                "max_speed": "2.25马赫",
                "combat_radius": "1600km",
                "max_altitude": "18000m",
                "armament": "30mm机炮, 空空导弹, 空地导弹",
                "radar": "雪豹-E相控阵雷达"
            }
        },
        "99A主战坦克": {
            "status": "found",
            "weapon_data": {
                "weapon_name": "99A主战坦克",
                "weapon_type": "主战坦克",
                "firepower_rating": 10,
                "protection_rating": 9,
                "mobility_rating": 8,
                "main_gun": "125mm滑膛炮",
                "armor_type": "复合装甲+反应装甲",
                "max_speed": "70km/h",
                "crew": 3,
                "weight": "54吨",
                "fire_control": "先进火控系统"
            }
        },
        "歼-20战斗机": {
            "status": "found",
            "weapon_data": {
                "weapon_name": "歼-20战斗机",
                "weapon_type": "战斗机",
                "firepower_rating": 10,
                "protection_rating": 8,
                "mobility_rating": 10,
                "max_speed": "2.5马赫",
                "combat_radius": "1200km",
                "max_altitude": "20000m",
                "stealth": "第五代隐身战机",
                "armament": "空空导弹, 空地导弹",
                "avionics": "先进航电系统"
            }
        }
    }
    
    return knowledge_db.get(weapon_name, {
        "status": "not_found",
        "weapon_data": None,
        "message": f"武器知识库中未找到 {weapon_name}({weapon_type})"
    })

def build_comprehensive_prompt_safe(enemy_units: List[Dict], friendly_units: List[Dict], 
                                   enemy_knowledge: List[Dict], friendly_knowledge: List[Dict]) -> Dict[str, Any]:
    """安全的综合战场分析提示词构建"""
    start_time = time.time()
    logger.info("开始构建综合战场分析提示词")
    
    try:
        from src.llm_prompt_builder import LLMPromptBuilder
        
        prompt_builder = LLMPromptBuilder()
        
        # 构建综合战场数据
        battlefield_data = {
            "status": "success",
            "battlefield_data": {
                "scenario_id": f"BATTLE_SCENARIO_{int(time.time())}",
                "timestamp": datetime.now().isoformat(),
                "enemy_units": enemy_units,
                "friendly_units": friendly_units,
                "total_enemy_count": len(enemy_units),
                "total_friendly_count": len(friendly_units)
            },
            "enemy_knowledge": enemy_knowledge,
            "friendly_knowledge": friendly_knowledge,
            "tactical_situation": {
                "terrain": "平原地形",
                "weather": "晴朗",
                "visibility": "良好",
                "time_of_day": "白天"
            }
        }
        
        # 使用提示词构建器
        prompt_data = prompt_builder.build_comprehensive_battle_prompt(battlefield_data)
        
        processing_time = round((time.time() - start_time) * 1000, 2)
        
        logger.info(f"综合提示词构建完成 - 耗时: {processing_time}ms")
        logger.info(f"系统提示词长度: {len(prompt_data['system_prompt'])} 字符")
        logger.info(f"用户提示词长度: {len(prompt_data['user_prompt'])} 字符")
        
        return {
            "prompt_data": prompt_data,
            "processing_time_ms": processing_time
        }
        
    except Exception as e:
        processing_time = round((time.time() - start_time) * 1000, 2)
        logger.error(f"提示词构建失败: {e}")
        return {
            "prompt_data": None,
            "processing_time_ms": processing_time,
            "error": str(e)
        }

def call_llm_analysis_safe(prompt_data: Dict[str, str]) -> Dict[str, Any]:
    """安全的LLM分析调用"""
    start_time = time.time()
    logger.info("开始调用大模型进行战场分析")

    try:
        from src.llm_service import LLMService

        # 初始化LLM服务
        llm_service = LLMService()

        logger.info("LLM服务配置:")
        logger.info(f"  提供商: {llm_service.config.get('provider')}")
        logger.info(f"  模型: {llm_service.config.get('model')}")
        logger.info(f"  超时设置: {llm_service.config.get('timeout')}s")

        # 记录发送的提示词
        logger.info("=== 系统提示词 ===")
        logger.info(prompt_data["system_prompt"])
        logger.info("=== 用户提示词 ===")
        logger.info(prompt_data["user_prompt"])
        logger.info("=== 提示词结束 ===")

        # 执行分析
        result = llm_service.analyze_battlefield_situation(
            prompt_data["system_prompt"],
            prompt_data["user_prompt"]
        )

        processing_time = round((time.time() - start_time) * 1000, 2)

        if result["status"] == "success":
            logger.info(f"大模型分析成功 - 耗时: {processing_time}ms")
            logger.info(f"消耗tokens: {result.get('tokens_used')}")
            logger.info(f"使用模型: {result.get('model')}")

            # 记录完整分析结果
            logger.info("=== 大模型分析结果 ===")
            analysis_result = result.get("analysis_result", "")
            if isinstance(analysis_result, dict):
                logger.info(json.dumps(analysis_result, ensure_ascii=False, indent=2))
            else:
                logger.info(str(analysis_result))
            logger.info("=== 分析结果结束 ===")

        else:
            logger.error(f"大模型分析失败: {result.get('error_message')}")

        return {
            "result": result,
            "processing_time_ms": processing_time
        }

    except Exception as e:
        processing_time = round((time.time() - start_time) * 1000, 2)
        logger.error(f"大模型调用异常: {e}")

        return {
            "result": {
                "status": "error",
                "error_message": str(e)
            },
            "processing_time_ms": processing_time
        }

async def analyze_battlefield_data(battlefield_data: BattlefieldData) -> AnalysisResult:
    """分析战场数据的核心函数"""
    global latest_analysis_result, analysis_history

    analysis_start_time = time.time()
    analysis_id = f"ANALYSIS_{int(time.time() * 1000)}"

    logger.info("=" * 80)
    logger.info(f"开始战场态势分析 - ID: {analysis_id}")
    logger.info(f"模拟时间: {battlefield_data.simulation_time}分钟")
    logger.info("=" * 80)

    try:
        # 1. 转换数据格式
        enemy_units = [unit.model_dump() for unit in battlefield_data.enemy_units]
        friendly_units = [unit.model_dump() for unit in battlefield_data.friendly_units]

        logger.info(f"接收到 {len(enemy_units)} 个敌方单位, {len(friendly_units)} 个我方单位")

        # 记录原始单位信息
        logger.info("=== 原始敌方单位信息 ===")
        for unit in enemy_units:
            logger.info(json.dumps(unit, ensure_ascii=False, indent=2))

        logger.info("=== 原始我方单位信息 ===")
        for unit in friendly_units:
            logger.info(json.dumps(unit, ensure_ascii=False, indent=2))

        # 2. 查询武器知识库
        logger.info("阶段1: 查询武器知识库")

        enemy_knowledge = []
        for unit in enemy_units:
            knowledge = query_weapon_knowledge_safe(
                unit["name"],
                unit["type"]
            )
            enemy_knowledge.append(knowledge)

        friendly_knowledge = []
        for unit in friendly_units:
            knowledge = query_weapon_knowledge_safe(
                unit["name"],
                unit["type"]
            )
            friendly_knowledge.append(knowledge)

        # 3. 构建综合提示词
        logger.info("阶段2: 构建综合提示词")
        prompt_result = build_comprehensive_prompt_safe(
            enemy_units, friendly_units,
            enemy_knowledge, friendly_knowledge
        )

        if prompt_result.get("error"):
            raise Exception(f"提示词构建失败: {prompt_result['error']}")

        # 4. 调用大模型分析
        logger.info("阶段3: 调用大模型分析")
        llm_result = call_llm_analysis_safe(prompt_result["prompt_data"])

        # 5. 生成分析结果
        total_time = round((time.time() - analysis_start_time) * 1000, 2)

        # 创建战场摘要
        battlefield_summary = {
            "analysis_id": analysis_id,
            "timestamp": datetime.now().isoformat(),
            "simulation_time": battlefield_data.simulation_time,
            "enemy_count": len(enemy_units),
            "friendly_count": len(friendly_units),
            "operational_enemy": len([u for u in enemy_units if u.get("status", {}).get("operational", False)]),
            "operational_friendly": len([u for u in friendly_units if u.get("status", {}).get("operational", False)]),
            "high_threats": len([u for u in enemy_units if u.get("threat_level") in ["高", "极高"]]),
            "processing_time_ms": total_time
        }

        # 解析LLM分析结果
        threat_assessment = None
        tactical_recommendation = None

        if llm_result["result"]["status"] == "success":
            try:
                llm_analysis = llm_result["result"].get("analysis_result", {})
                if isinstance(llm_analysis, str):
                    # 处理包含markdown代码块的JSON字符串
                    if "```json" in llm_analysis:
                        # 提取JSON代码块内容
                        start_marker = "```json"
                        end_marker = "```"
                        start_idx = llm_analysis.find(start_marker)
                        if start_idx != -1:
                            start_idx += len(start_marker)
                            end_idx = llm_analysis.find(end_marker, start_idx)
                            if end_idx != -1:
                                json_content = llm_analysis[start_idx:end_idx].strip()
                                llm_analysis = json.loads(json_content)
                            else:
                                logger.warning("未找到JSON代码块结束标记")
                                llm_analysis = {}
                        else:
                            logger.warning("未找到JSON代码块开始标记")
                            llm_analysis = {}
                    else:
                        # 直接解析JSON字符串
                        llm_analysis = json.loads(llm_analysis)

                threat_assessment = llm_analysis.get("威胁评估", {})
                tactical_recommendation = {
                    "战术建议": llm_analysis.get("战术建议", {}),
                    "作战方案": llm_analysis.get("作战方案", []),
                    "应急预案": llm_analysis.get("应急预案", {})
                }

                logger.info("LLM分析结果解析成功")

            except Exception as e:
                logger.warning(f"解析LLM分析结果失败: {e}")
                logger.info(f"原始LLM返回内容前100字符: {str(llm_result['result'].get('analysis_result', ''))[:100]}")
                # 设置默认值
                threat_assessment = None
                tactical_recommendation = None

        # 创建最终结果
        result = AnalysisResult(
            status="success",
            analysis_id=analysis_id,
            timestamp=datetime.now().isoformat(),
            processing_time_ms=total_time,
            battlefield_summary=battlefield_summary,
            threat_assessment=threat_assessment,
            tactical_recommendation=tactical_recommendation,
            llm_analysis=llm_result["result"] if llm_result["result"]["status"] == "success" else None,
            weapon_knowledge={
                "enemy_knowledge": enemy_knowledge,
                "friendly_knowledge": friendly_knowledge
            }
        )

        # 更新全局状态
        latest_analysis_result = result
        analysis_history.append(result)

        # 保持历史记录在合理范围内
        if len(analysis_history) > 100:
            analysis_history = analysis_history[-50:]
        
        # await broadcast_analysis_update(result)
        await server.broadcast_message(result, None)

        logger.info("=" * 80)
        logger.info(f"战场态势分析完成 - ID: {analysis_id}")
        logger.info(f"总处理时间: {total_time}ms")
        logger.info(f"LLM分析状态: {llm_result['result']['status']}")
        if llm_result["result"]["status"] == "success":
            logger.info(f"消耗tokens: {llm_result['result'].get('tokens_used')}")
        logger.info("=" * 80)

        return result

    except Exception as e:
        total_time = round((time.time() - analysis_start_time) * 1000, 2)
        logger.error(f"战场分析异常: {e}")

        # 返回错误结果
        error_result = AnalysisResult(
            status="error",
            analysis_id=analysis_id,
            timestamp=datetime.now().isoformat(),
            processing_time_ms=total_time,
            battlefield_summary={
                "analysis_id": analysis_id,
                "error": str(e),
                "processing_time_ms": total_time
            }
        )

        return error_result


async def broadcast_analysis_update(result: AnalysisResult):
    """广播分析结果更新给所有WebSocket客户端"""
    try:
        # 创建广播消息
        message = {
            "type": "analysis_update",
            "data": result.model_dump(),
            "timestamp": datetime.now().isoformat()
        }

        # 转换为JSON字符串
        message_json = json.dumps(message, ensure_ascii=False)
        # 广播给所有连接的客户端
        await manager.broadcast(message_json)

        logger.info(f"已广播分析结果更新: {result.analysis_id}")

    except Exception as e:
        logger.error(f"广播分析结果失败: {e}")
# FastAPI路由端点

# 启动事件已移至lifespan处理器

@app.get("/")
async def root():
    """根路径 - API信息"""
    return {
        "service": "战场态势分析API",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "endpoints": {
            "analyze": "/battlefield/analyze",
            "status": "/battlefield/status",
            "latest": "/battlefield/latest",
            "history": "/battlefield/history"
        }
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "battlefield-analysis-api"
    }

@app.post("/battlefield/analyze", response_model=AnalysisResult)
async def analyze_battlefield(
    battlefield_data: BattlefieldData,
    background_tasks: BackgroundTasks
):
    """
    分析战场数据

    接收实时战场数据，进行威胁评估和战术分析
    """
    try:
        logger.info(f"收到战场分析请求 - 模拟时间: {battlefield_data.simulation_time}分钟")

        # 执行分析
        result = await analyze_battlefield_data(battlefield_data)

        # 添加后台任务保存详细报告
        background_tasks.add_task(save_analysis_report, result)

        return result

    except Exception as e:
        logger.error(f"分析请求处理失败: {e}")
        raise HTTPException(status_code=500, detail=f"分析失败: {str(e)}")

# @app.get("/battlefield/latest", response_model=Optional[AnalysisResult])
# async def get_latest_analysis():
#     """获取最新的分析结果"""
#     global latest_analysis_result

#     if latest_analysis_result is None:
#         raise HTTPException(status_code=404, detail="暂无分析结果")

#     return latest_analysis_result

@app.get("/battlefield/history")
async def get_analysis_history(limit: int = 10):
    """获取分析历史记录"""
    global analysis_history

    if not analysis_history:
        return {
            "total": 0,
            "results": [],
            "message": "暂无历史记录"
        }

    # 返回最近的记录
    recent_history = analysis_history[-limit:] if len(analysis_history) > limit else analysis_history

    return {
        "total": len(analysis_history),
        "returned": len(recent_history),
        "results": recent_history
    }

@app.get("/battlefield/status")
async def get_system_status():
    """获取系统状态"""
    global latest_analysis_result, analysis_history

    return {
        "system_status": "running",
        "timestamp": datetime.now().isoformat(),
        "statistics": {
            "total_analyses": len(analysis_history),
            "latest_analysis_time": latest_analysis_result.timestamp if latest_analysis_result else None,
            "latest_analysis_id": latest_analysis_result.analysis_id if latest_analysis_result else None
        },
        "service_info": {
            "name": "战场态势分析API",
            "version": "1.0.0",
            "description": "实时战场数据分析和威胁评估系统"
        }
    }

@app.delete("/battlefield/history")
async def clear_analysis_history():
    """清空分析历史记录"""
    global analysis_history

    count = len(analysis_history)
    analysis_history.clear()

    logger.info(f"清空了 {count} 条历史记录")

    return {
        "status": "success",
        "message": f"已清空 {count} 条历史记录",
        "timestamp": datetime.now().isoformat()
    }

async def save_analysis_report(result: AnalysisResult):
    """保存分析报告到文件（后台任务）"""
    try:
        # 创建reports目录
        if not os.path.exists("reports"):
            os.makedirs("reports")

        # 生成报告文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"reports/analysis_report_{result.analysis_id}_{timestamp}.json"

        # 保存报告
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(result.model_dump(), f, ensure_ascii=False, indent=2)

        logger.info(f"分析报告已保存: {report_filename}")

    except Exception as e:
        logger.error(f"保存分析报告失败: {e}")

@app.websocket("/battlefield/latest")
async def websocket_latest_analysis(websocket: WebSocket):
    """WebSocket端点 - 实时推送最新分析结果"""
    print("WebSocket处理函数被调用")
    await manager.connect(websocket)
    print("连接成功")

    try:
        print("开始WebSocket消息循环")
        while True:
            try:
                print("等待接收客户端消息...")
                # 接收客户端消息（心跳包等）
                message = await websocket.receive_text()
                print(f"收到原始消息: {message}")

                # 处理客户端请求
                client_message = json.loads(message)
                print("接收到消息")
                print(client_message)

                if client_message.get("type") == "ping":
                    # 响应心跳包
                    pong_message = {
                        "type": "pong",
                        "timestamp": datetime.now().isoformat()
                    }
                    print(f"发送pong响应: {pong_message}")
                    await websocket.send_text(json.dumps(pong_message, ensure_ascii=False))
                    print("pong响应已发送")

            except json.JSONDecodeError as e:
                print(f"JSON解析错误: {e}")
                error_message = {
                    "type": "error",
                    "message": "Invalid JSON format",
                    "timestamp": datetime.now().isoformat()
                }
                await websocket.send_text(json.dumps(error_message, ensure_ascii=False))
            except Exception as e:
                print(f"处理消息时出错: {e}")
                # 继续循环，不断开连接

    except WebSocketDisconnect:
        print("WebSocket连接断开")
        manager.disconnect(websocket)
    except Exception as e:
        print(f"WebSocket处理出错: {e}")
        manager.disconnect(websocket)

class ChatCompletionRequest(BaseModel):
    session_id: str
    is_new_session: bool
    question: str

@app.post("/chat/completions")
async def chat_completions(request: ChatCompletionRequest):
    """
    非流式聊天接口 - 一次性返回完整响应
    """
    # try:
    from models import DialogMessage, async_session
    from config.prompt import prompt_template
    from openai import AsyncOpenAI, OpenAI
    from sqlalchemy import desc, select
    client = AsyncOpenAI(base_url="http://localhost:8000/v1", api_key="123456")
    question = request.question

    try:
        # 读取现有数据
        with open("raw_data.json", 'r', encoding='utf-8') as f:
            raw_data = json.load(f)
    except FileNotFoundError:
        # 如果文件不存在，创建空列表
        raw_data = []

    # 确保raw_data不为空
    if raw_data:
        latest_data = raw_data[-1] if isinstance(raw_data, list) else raw_data
    else:
        latest_data = "暂无战场数据"

    user_message = f"真实数据:\n```json\n{latest_data}```\n\n用户问题:\n{question}"

    async with async_session() as session:
        # 获取最后一条消息的dialog_id
        stmt = select(DialogMessage).order_by(desc(DialogMessage.message_id)).limit(1)
        session_result = await session.execute(stmt)
        last_message = session_result.scalar_one_or_none()

        history_messages = []
        dialog_id = last_message.dialog_id if last_message else None

        if request.is_new_session:
            if dialog_id:
                dialog_id += 1
            else:
                dialog_id = 1
        else:
            if dialog_id:
                # 获取当前对话的历史消息
                history_stmt = select(DialogMessage).where(DialogMessage.dialog_id == dialog_id).order_by(DialogMessage.message_id)
                history_result = await session.execute(history_stmt)
                history_records = history_result.scalars().all()

                for message in history_records:
                    msg = {
                        "role": message.role,
                        "content": message.content
                    }
                    history_messages.append(msg)
            else:
                dialog_id = 1

        # 添加当前用户消息
        # history_messages.append({"role": "user", "content": user_message})

        # 构建完整的消息列表
        messages = [{"role": "system", "content": prompt_template}]
        messages.extend(history_messages)

        # 调用 LLM
        response = await client.chat.completions.create(
            messages=[{"role": "system", "content": prompt_template}, {"role": "user", "content": question}],
            model="Qwen2.5-72B-Instruct-GPTQ-Int4",
            temperature=0.7,
            stream=False
        )

        assistant_content = response.choices[0].message.content
        import random

        # 生成0到3之间的随机整数（包含0和3）
        random_number = random.randint(0, 3)
        content = {"type": random_number, "content": assistant_content}

        # 保存对话记录到数据库
        question_message = DialogMessage(
            dialog_id=dialog_id,
            content=question,
            role="user"
        )
        answer_message = DialogMessage(
            dialog_id=dialog_id,
            content=assistant_content,
            role="assistant"
        )

        session.add(question_message)
        session.add(answer_message)
        await session.commit()

    return content

if __name__ == "__main__":
    # 运行FastAPI服务
    uvicorn.run(
        "main_fastapi:app",
        host="0.0.0.0",
        port=8010,
        # reload=True,
        log_level="info"
    )
